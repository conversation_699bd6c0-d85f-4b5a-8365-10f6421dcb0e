//* PACKAGES
import React, { useRef } from 'react'
import { router } from '@inertiajs/react';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdOutlinePersonOutline,
    Md<PERSON>ut<PERSON><PERSON><PERSON><PERSON>erson,
    Md<PERSON>erifiedUser,
    MdMode
} from "react-icons/md";

//* COMPONENTS

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 

//* UTILS

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function User({ item }) {
    //! PACKAGE
    const ref = useRef();

    //! HOOKS

    //! VARIABLES
    //...

    //! STATES

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const handleView = () => {
        router.get(route("client.transaction.threshold-view"), {
            id: item.id,
            email: item.email
        });
    };

    return (
        <tr className="hover:bg-gray-100 ">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <div className=" text-2xl border p-1 rounded-full">
                        <MdOutlinePersonOutline />
                    </div>
                    <span className=" font-semibold cursor-pointer">
                        {item.name}
                    </span>
                </label>
            </td>
            <td>
                <span>{item.email}</span>
            </td>
            <td>
                {item.is_active ? (
                    <div className="text-green-500 inline-flex space-x-1 items-center">
                        <MdVerifiedUser />
                        <span>Enabled</span>
                    </div>
                ) : (
                    <div className="text-gray-400 inline-flex space-x-1 items-center">
                        <MdOutlineLockPerson />
                        <span>Disabled</span>
                    </div>
                )}
            </td>
            <td>
                <span className="flex justify-center">
                    <button
                        className="inline-flex items-center text-gray-500 hover:text-primary"
                        onClick={handleView}
                    >
                        <MdMode /> Edit
                    </button>

                </span>
            </td>
        </tr>
    );
}
