import AdminLayout from "@/Layouts/AdminLayout";
import { Link, router } from "@inertiajs/react";
import {
    MdOutlineSettings
} from "react-icons/md";
import ExtensionFeeItem from "../../../Components/Setting/ExtensionFee/ExtensionFeeItem";

export default function ExtensionFeeDefault({
    name = "",
    userId = 0
}) {
    const paramExtension = route().params.extension;

    // console.log(fees);

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1100px] mt-20 flex flex-col space-y-10 px-8">
                <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                    <span className="font-semibold">
                        Edit Transaction Threshold
                    </span>
                </div>

                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <span className="flex items-center pl-2 space-x-2">Type</span>
                                </th>
                                <th>
                                    <span>Base Fee</span>
                                </th>
                                <th>
                                    <span>Adjustments (-/+)</span>
                                </th>
                                <th>
                                    <span>Total Fee</span>
                                </th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {paramExtension && Object.keys(fees[paramExtension]).map((key, index) => (
                                <ExtensionFeeItem
                                    item={fees[paramExtension][key]}
                                    key={"efi-" + index}
                                    extension_type={extension_type}
                                />
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </AdminLayout >
    );
}
