<?php

namespace App\Modules\Setting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Requests\ShowListRequest;
use App\Modules\Setting\Constants\ExtensionFeeType;
use App\Modules\Setting\Requests\ExtensionFeeDeleteRequest;
use App\Modules\Setting\Requests\ExtensionFeeUpdateRequest;
use App\Modules\Setting\Services\ExtensionFeeService;
use App\Util\Constant\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class TransactionThresholdController extends Controller
{
    public function get_default()
    {
        return Inertia::render('Setting/Transaction/Default', [
            'fees' => ExtensionFeeService::instance()->getDefault(),
            'extension_type' => ExtensionFeeType::SETTINGS,
        ]);
    }

    public function update(ExtensionFeeUpdateRequest $request)
    {
        $validator = $request->update();

        if ($validator && $validator->errors()) {
            return redirect()->back()->withErrors($validator->errors()->messages());
        }

        return redirect()->back();
    }

    public function get_users(ShowListRequest $request)
    {
        $data = $request->showUsers();

        return Inertia::render('Client/TransactionThreshold/Index', $data);
    }

    public function get_user_custom(Request $request)
    {
        $userId = $request->id;

        $data['userId'] = $userId;
        $data['email'] = $request->email;
        $data['transactions'] = DB::client()->table('user_transactions')
            ->select('user_transactions.*', 'transactions.name as transaction_name', 'transactions.user_limit')
            ->join('transactions', 'transactions.id', '=', 'user_transactions.transaction_id')
            ->where('user_transactions.user_id', $userId)
            ->whereIn('transactions.name', array_keys(Transaction::TYPES))
            ->orderBy('transactions.name')
            ->get();

        return Inertia::render('Client/TransactionThreshold/View', $data);
    }

    public function updateCustomLimit(Request $request)
    {
        DB::client()->table('user_transactions')
            ->where('user_id', $request->userId)
            ->where('transaction_id', $request->transactionId)
            ->increment('custom_limit', $request->adjustment);

        return Inertia::location(route('client.transaction.threshold-view', [
            'id' => $request->userId,
            'email' => $request->email,
        ]));
    }

    public function updateMultipleCustomLimits(Request $request)
    {
        dd($request->all());
        DB::client()->table('user_transactions')
            ->where('user_id', $request->userId)
            ->where('transaction_id', $request->transactionId)
            ->increment('custom_limit', $request->adjustment);

        return Inertia::location(route('client.transaction.threshold-view', [
            'id' => $request->userId,
            'email' => $request->email,
        ]));
    }
}
