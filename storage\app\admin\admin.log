[2025-08-29 02:15:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Processed 1 expired requests  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-29 02:17:23] local.ERROR: {"query":{"statusType":"APPROVED"},"parameter":{"statusType":"APPROVED"},"error":"ParseError","message":"syntax error, unexpected token \",\", expecting identifier or variable or \"{\" or \"$\"","code":0}  
[2025-08-29 02:44:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:45:28] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:11] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:15] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:18] local.ERROR: {"query":{"statusType":"DELETED","orderby":"email:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"email:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:21] local.ERROR: {"query":{"statusType":"DELETED","orderby":"domain:asc","email":null},"parameter":{"statusType":"DELETED","orderby":"domain:asc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:26] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:32] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:39] local.ERROR: {"query":{"statusType":"ALL","email":"asd"},"parameter":{"statusType":"ALL","email":"asd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:59:00] local.ERROR: {"query":{"orderby":"requested_at:desc"},"parameter":{"orderby":"requested_at:desc"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column domain_cancellation_requests.created_at does not exist
LINE 1: ...n_id\" = \"registered_domains\".\"domain_id\" order by \"domain_ca...
                                                             ^ (Connection: client, SQL: select \"domains\".\"name\" as \"domainName\", \"domains\".\"status\", \"domains\".\"deleted_at\" as \"domainDeletedAt\", \"domains\".\"created_at\", \"users\".\"id\" as \"user_id\", \"users\".\"email\", \"users\".\"first_name\", \"users\".\"last_name\", \"domain_cancellation_requests\".\"deleted_at\", \"domain_cancellation_requests\".\"requested_at\", \"domain_cancellation_requests\".\"reason\", \"domain_cancellation_requests\".\"id\" as \"dcrID\", \"domain_cancellation_requests\".\"support_agent_id\", \"domain_cancellation_requests\".\"support_agent_name\", \"domain_cancellation_requests\".\"support_note\", \"domain_cancellation_requests\".\"feedback_date\", \"domain_cancellation_requests\".\"domain_id\", \"registered_domains\".\"status\" as \"rstatus\" from \"domain_cancellation_requests\" inner join \"domains\" on \"domain_cancellation_requests\".\"domain_id\" = \"domains\".\"id\" inner join \"users\" on \"domain_cancellation_requests\".\"user_id\" = \"users\".\"id\" inner join \"registered_domains\" on \"domain_cancellation_requests\".\"domain_id\" = \"registered_domains\".\"domain_id\" order by \"domain_cancellation_requests\".\"created_at\" desc limit 20 offset 0)","code":"42703"}  
[2025-08-29 02:59:32] local.ERROR: {"query":{"email":"asd"},"parameter":{"email":"asd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:59:36] local.ERROR: {"query":{"email":"zcasd"},"parameter":{"email":"zcasd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 03:02:43] local.ERROR: {"query":{"email":"asdd"},"parameter":{"email":"asdd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 03:02:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 03:02:57] local.ERROR: {"query":{"email":"asdasd"},"parameter":{"email":"asdasd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 06:47:34] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-01 08:14:28] local.INFO: user login from 127.0.0.1  
[2025-09-01 09:23:33] local.INFO: user login from 127.0.0.1  
[2025-09-02 01:16:00] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-09-02 01:18:21] local.INFO: user login from 127.0.0.1  
[2025-09-02 02:02:23] local.ERROR: {"query":[],"parameter":{"domainName":"limittestme.net","userEmail":"<EMAIL>","domainId":112,"createdDate":"2025-08-19 09:42:03","userID":7,"support_note":"test"},"error":"Illuminate\\Validation\\ValidationException","message":"The support note field must be at least 10 characters.","code":0}  
[2025-09-02 02:02:28] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
[2025-09-02 04:58:09] local.ERROR: {"query":{"statusType":"PENDING"},"parameter":{"statusType":"PENDING"},"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-09-02 05:09:46] local.ERROR: {"query":{"statusType":"PENDING"},"parameter":{"statusType":"PENDING"},"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-09-02 05:09:58] local.INFO: user login from 127.0.0.1  
[2025-09-02 05:11:55] local.ERROR: {"query":[],"parameter":{"domainId":125,"createdDate":"2025-08-27 01:16:37","support_note":"Domain deletion request rejected."},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"email_tracks\" does not exist
LINE 1: insert into \"email_tracks\" (\"email\", \"subject\", \"body\", \"dom...
                    ^ (Connection: client, SQL: insert into \"email_tracks\" (\"email\", \"subject\", \"body\", \"domain_id\", \"created_at\", \"updated_at\") values (<EMAIL>, Domain Deletion Request Rejected, Your request to delete the domain \"intersive.org\" has been rejected by our support team. If you have any questions, please contact our support., 125, 2025-09-02 05:11:55, 2025-09-02 05:11:55))","code":"42P01"}  
[2025-09-02 05:50:25] local.INFO: log out  
[2025-09-02 05:50:29] local.INFO: user login from 127.0.0.1  
[2025-09-02 05:54:22] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-02 06:01:09] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-02 06:01:19] local.ERROR: {"query":[],"parameter":{"domainName":"intersive.org","userEmail":"<EMAIL>","domainId":125,"createdDate":"2025-08-27 01:16:37","userID":7,"email":"<EMAIL>"},"error":"Symfony\\Component\\Mailer\\Exception\\TransportException","message":"Failed to authenticate on SMTP server with username \"5e46f43dd81cb4\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\".","code":535}  
[2025-09-02 06:01:26] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 06:02:25] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 06:03:34] local.ERROR: {"query":[],"parameter":{"domainId":112,"createdDate":"2025-08-19 09:42:03","support_note":"Domain deletion request rejected."},"error":"Symfony\\Component\\Mailer\\Exception\\TransportException","message":"Failed to authenticate on SMTP server with username \"5e46f43dd81cb4\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\".","code":535}  
[2025-09-02 06:04:22] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
[2025-09-02 06:04:25] local.ERROR: {"query":[],"parameter":{"domainName":"intersive.org","userEmail":"<EMAIL>","domainId":125,"createdDate":"2025-08-27 01:16:37","userID":7,"support_note":"asdzxc asd asd"},"error":"Symfony\\Component\\Mailer\\Exception\\TransportException","message":"Failed to authenticate on SMTP server with username \"5e46f43dd81cb4\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\".","code":535}  
[2025-09-02 06:22:44] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-02 06:22:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 06:23:15] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 06:50:32] local.ERROR: {"query":[],"parameter":{"domainName":"palwataw.com","userEmail":"<EMAIL>","domainId":129,"createdDate":"2025-08-27 01:16:37","userID":7,"support_note":"test  test et est es"},"error":"Error","message":"Call to undefined method App\\Modules\\RequestDelete\\Services\\DomainDeleteService::cancelDeleteRequest()","code":0}  
[2025-09-02 07:12:55] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-02 09:30:52] local.INFO: user login from 127.0.0.1  
[2025-09-02 09:45:50] local.INFO: array (
  'domainName' => 'priceties.com',
  'userEmail' => '<EMAIL>',
  'domainId' => 135,
  'createdDate' => '2025-09-02 07:15:54',
  'userID' => 7,
  'support_note' => 'asdxcasdadas',
  'email' => '<EMAIL>',
)  
[2025-09-02 09:46:15] local.ERROR: {"query":[],"parameter":{"domainName":"priceties.com","userEmail":"<EMAIL>","domainId":135,"createdDate":"2025-09-02 07:15:54","userID":7,"support_note":"asdxcasdadas","email":"<EMAIL>"},"error":"ErrorException","message":"Trying to access array offset on value of type null","code":0}  
[2025-09-02 09:46:17] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 09:48:49] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 09:49:07] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-02 09:51:05] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
