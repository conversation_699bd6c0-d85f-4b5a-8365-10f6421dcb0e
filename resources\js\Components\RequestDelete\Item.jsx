import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useRef, useState } from "react";
import {
    MdMoreVert,
    MdOutlineCheck,
    MdOutlineSafetyCheck,
    MdClose,
} from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import getRecentTime from "@/Util/getRecentTime";
import getRemainingTime from "@/Util/getRemainingTime";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";
import { router } from "@inertiajs/react";
import { _PendingDelete } from "@/Constant/_PendingDelete";
import ShowMoreLess from "../ShowMoreLess";
import DomainDeletionModal from "../RequestDelete/RequestDeletion";
import { StatusBadge } from "./StatusBadge";
import convertToTitleCase from "@/Util/convertToTitleCase";
import ConfirmDomainDeletionModal from "./ConfirmRequestDeletion";
import { toast } from "react-toastify";
import RejectDomainDeletionModal from "./RejectRequestDeletion";
import ApproveDomainDeletionModal from "./ApproveRequestDeletion";

export default function Item({
    item,
    isSelected,
    onCheckboxChange,
    statusType,
}) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const [selectedRequest, setSelectedRequest] = useState(null);
    const [modalOpen, setModalOpen] = useState(false);
    const [modalOpenConfirm, setModalOpenConfirm] = useState(false);
    const [modalOpenReject, setModalOpenReject] = useState(false);

    const openModal = (request) => {
        setSelectedRequest(request);
        setModalOpen(true);
    };
    const openModalConfirm = (request) => {
        setSelectedRequest(request);
        setModalOpenConfirm(true);
    };
    const openModalReject = (request) => {
        setSelectedRequest(request);
        setModalOpenReject(true);
    };

    const openModalApprove = (request) => {
        setSelectedRequest(request);
        setModalOpenApprove(true);
    };

    const handleReject = (requestID) => {
        router.post(
            route("domain.delete-request.reject"),
            { id: requestID },
            {
                onSuccess: () => {
                    toast.success("Domain deletion rejected successfully.");
                    console.log(requestID);
                },
                onError: (errors) => {
                    toast.error(
                        "Failed to reject the domain deletion request. Please try again."
                    );
                    console.error(errors);
                },
            }
        );
    };

    const [more, setMore] = useState(false);
    const [morer, setMorer] = useState(false);

    return (
        <>
            <tr className="">
                <td>
                    <label className="flex items-center pl-2 space-x-2">
                        <span className="cursor-pointer">
                            {item.domainName}
                        </span>
                    </label>
                </td>
                <td>
                    <span>{item.email}</span>
                </td>
                <td>
                    <div className="w-full break-all">
                        {item.reason?.trim() ? (
                            item.reason.length > 60 ? (
                                <>
                                    <div className="w-[250px]">
                                        <span
                                            className={`block w-full leading-5 ${
                                                morer ? "" : "truncate"
                                            }`}
                                            title={item.reason}
                                        >
                                            {item.reason}
                                        </span>
                                    </div>
                                    <span
                                        className="text-link cursor-pointer mt-1 inline-block"
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            setMorer(!morer);
                                        }}
                                    >
                                        {morer ? "show less" : "more"}
                                    </span>
                                </>
                            ) : (
                                <div className="w-full text-justify leading-5">
                                    {item.reason}
                                </div>
                            )
                        ) : null}
                    </div>
                </td>

                <td>
                    <span>{item.rstatus}</span>
                </td>
                <td>
                    {/* <span>{item.rstatus}</span> */}
                    {<StatusBadge status={convertToTitleCase(item.status)} />}
                </td>
                <td>
                    {/* <button
                        className="bg-blue-400 hover:bg-blue-600 text-white px-4 py-1 rounded w-3/4"
                        onClick={() => openModal(item)}
                    >
                        View
                    </button> */}
                    {item.requested_at}
                </td>
                <td>
                    <div className="w-full break-all">
                        {item.support_note?.trim() ? (
                            item.support_note.length > 60 ? (
                                <>
                                    <div className="w-[250px]">
                                        <span
                                            className={`${
                                                more ? "" : "truncate"
                                            } block w-full leading-5`}
                                            title={item.support_note}
                                        >
                                            {item.support_note}
                                        </span>
                                    </div>
                                    <span
                                        className="text-link cursor-pointer mt-1 inline-block"
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            setMore(!more);
                                        }}
                                    >
                                        {more ? "show less" : "more"}
                                    </span>
                                </>
                            ) : (
                                <div className="w-full text-justify leading-5">
                                    {item.support_note}
                                </div>
                            )
                        ) : null}
                    </div>
                </td>
                <td>
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            <button
                                className={`px-5 py-1 justify-start flex ${
                                    item.support_agent_id != null
                                        ? "text-gray-200 px-2 py-1 cursor-not-allowed"
                                        : "text-black px-2 py-1 hover:bg-gray-100"
                                }`}
                                onClick={() => openModalConfirm(item)}
                                disabled={item.support_agent_id != null}
                            >
                                Approve
                            </button>
                            <button
                                className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                    item.support_agent_id != null
                                        ? "text-gray-200 px-2 py-1 cursor-not-allowed"
                                        : "text-black px-2 py-1 hover:bg-gray-100"
                                }`}
                                onClick={() => openModalReject(item)}
                                disabled={item.support_agent_id != null}
                            >
                                Reject
                            </button>
                        </DropDownContainer>
                    </span>
                </td>
            </tr>
            {/* Headless UI Modal */}
            {modalOpen && selectedRequest && (
                <DomainDeletionModal
                    isOpen={modalOpen}
                    onClose={() => setModalOpen(false)}
                    deletionRequest={selectedRequest}
                />
            )}
            {modalOpenConfirm && selectedRequest && (
                <ConfirmDomainDeletionModal
                    isOpen={modalOpenConfirm}
                    onClose={() => setModalOpenConfirm(false)}
                    deletionRequest={selectedRequest}
                    onRequestSubmit={() => setModalOpenConfirm(true)} // <-- Trigger confirm modal
                />
            )}
            {modalOpenReject && selectedRequest && (
                <RejectDomainDeletionModal
                    isOpen={modalOpenReject}
                    onClose={() => setModalOpenReject(false)}
                    deletionRequest={selectedRequest}
                />
            )}
        </>
    );
}
