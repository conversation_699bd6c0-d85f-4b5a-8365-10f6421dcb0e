//* PACKAGES
import React, { useState, useEffect } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,

} from "react-icons/md";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Filter from "@/Components/Client/Filter";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";
import User from '@/Components/Client/TransactionThreshold/User';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index(
    {
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        itemName = "item",
        filters = {},
    }
) {
    //! PACKAGE
    const query = route().params;

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    //...

    //! STATES
    const [selectAll, setSelectAll] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [selectedActive, setSelectedActive] = useState([]);
    const [selectedInactive, setSelectedInactive] = useState([]);
    const [limit, setLimit] = useState(parseInt(query.limit) || 20);
    const [hasSpinner, setSpinner] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const resetStates = () => {
        setSelectAll(false);
        setSelectedUsers([]);
        setSelectedActive([]);
        setSelectedInactive([]);
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const handleLimitChange = (e) => {
        const newLimit = parseInt(getEventValue(e));
        setLimit(newLimit);
        router.get(route("client.extension.fee"), { ...route().params, limit: newLimit, page: 1 }, {
            preserveScroll: true,
            preserveState: true,
        });
    };

    return (
        <AdminLayout>
            <div
                className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4"
            >
                <div className="mb-4">
                    <h1 className="text-4xl font-bold">Transaction Threshold</h1>
                    <p className="text-gray-600">
                        Overview of limits set for transactions and the actions triggered when they're exceeded.
                    </p>
                </div>
                <div className='flex items-center space-x-10'>
                    <div
                        className="flex justify-start"
                    >
                        <label className="mr-2 text-sm pt-1 text-gray-600">
                            Show
                        </label>
                        <select
                            value={limit}
                            onChange={handleLimitChange}
                            className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                        >
                            {[20, 25, 30, 40, 50, 100].map((val) => (
                                <option key={val} value={val}>
                                    {val}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div
                        id="sample"
                        className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                    >
                        <label className="flex items-center">
                            <MdOutlineFilterAlt />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>

                        <Filter
                            parent="extension_fee"
                        />
                    </div>
                </div>

                <div className="relative">
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-5 space-x-2">
                                        <span className="">User</span>
                                    </label>
                                </th>
                                <th>
                                    <span>Email</span>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>
                                <th>
                                    <span className="flex justify-center text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {items.map((user, index) => (
                                        <User item={user} key={"dl-" + index} />
                                    ))}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>
                {hasSpinner ? " " :
                    (
                        <>
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName={itemName}
                            />
                        </>
                    )
                }
            </div>
        </AdminLayout>
    );
}
